// utils/toastControl.ts
import { toast, ToastOptions } from "react-hot-toast";

let allowToasts: boolean = true;

/**
 * Enable or disable toast notifications globally.
 * If disabled, all queued toasts are removed.
 */
export const setAllowToasts = (value: boolean): void => {
  allowToasts = value;
  if (!value) {
    toast.remove(); // Remove existing and queued toasts
  }
};

export const safeToast = {
  success: (message: string, options?: ToastOptions) => {
    if (allowToasts) toast.success(message, options);
  },
  error: (message: string, options?: ToastOptions) => {
    if (allowToasts) toast.error(message, options);
  },
  // Additional wrappers can be added here if needed
};
