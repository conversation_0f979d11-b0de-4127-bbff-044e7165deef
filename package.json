{"name": "electronicbit-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"axios": "^1.11.0", "formik": "^2.4.6", "lucide-react": "^0.536.0", "next": "15.4.6", "nextjs-toploader": "^3.8.16", "react": "19.1.0", "react-dom": "19.1.0", "yup": "^1.7.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "typescript": "^5"}}