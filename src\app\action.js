"use server"

import axios from "axios";
import { cookies } from "next/headers";
const Services = axios.create({
    baseURL: process.env.NEXT_PUBLIC_BASE_URL,
    headers: {
        Accept: "application/json",
        // Authorization: `Bearer ${authStorage?.getAuthToken() ? authStorage?.getAuthToken() : ""}`,
        // 'X-RapidAPI-Key': `${NEXT_API_KEY}`,
    },
});

Services.interceptors.request.use(
    async (config) => {
        const cookieStore = await cookies()
        const token = cookieStore.get("SAID")?.value;
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => Promise.reject(error)
);

const apiType = {
    get: "GET",
    post: "POST",
    patch: "PATCH",
    delete: "DELETE"
}

export const sendAPIRequest = async (
    api,
    payload
) => {
    try {
        let response;
        switch (api.type) {
            case "POST":
                response = await Services.post(api.endpoint, payload);
                break;

            case "PUT":
                response = await Services.put(api.endpoint, payload);

                break;
            case "DELETE":
                response = await Services.delete(api.endpoint);
                break;

            case "PATCH":
                response = await Services.patch(api.endpoint, payload);
                break;

            default:
                const queryParams = buildQueryString(payload);
                // const queryParams = URLSearchParams(payload)?.toString();

                // console.dir(queryParams, "query", api)

                response = await Services.get(api.endpoint + queryParams);
                break;
        }

        return response?.data;
    } catch (error) {
        // console.log(error?.response)
        return error?.response?.data;
    }
}