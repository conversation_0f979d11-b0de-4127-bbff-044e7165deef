"use client";

import { safeToast } from "@/utils/safeToast";
import { useRouter } from "nextjs-toploader/app";
import { useCallback, useState } from "react";
import { isFunctionEmpty } from "../function";

type ApiFunction<P = any, R = any> = (payload: P) => Promise<R>;
type ResponseHandler<R = any> = (response: R) => void;
type ErrorHandler = (error: any) => void;

interface ApiResponse {
  status?: number | string;
  message?: string;
  response?: { status?: number };
  [key: string]: any;
}

interface UseApiRequestReturn<P = any, R = any> {
  isLoading: boolean;
  sendRequest: (
    functionCall: ApiFunction<P, ApiResponse>,
    responseHandler?: ResponseHandler<ApiResponse>,
    payload?: P,
    successMessage?: string,
    errorHandler?: ErrorHandler | null
  ) => Promise<void>;
}

const useApiRequest = <P = any, R = ApiResponse>(
  defaultLoading: boolean = true
): UseApiRequestReturn<P, R> => {
  const [isLoading, setIsLoading] = useState<boolean>(defaultLoading);
  const router = useRouter();

  const sendRequest = useCallback(
    async (
      functionCall: ApiFunction<P, R>,
      responseHandler?: ResponseHandler<R>,
      payload?: P,
      successMessage: string = "",
      errorHandler: ErrorHandler | null = null
    ) => {
      setIsLoading(true);

      try {
        const data = await functionCall(payload as P);

        if (
          (data as ApiResponse)?.status === 200 ||
          (data as ApiResponse)?.status === "success"
        ) {
          if (responseHandler) {
            responseHandler(data);
            if (successMessage) safeToast.success(successMessage);
          }
        } else {
          throw data;
        }
      } catch (error: any) {
        console.error(error);
        setIsLoading(false);

        if (errorHandler && !isFunctionEmpty(errorHandler)) {
          errorHandler(error);
        }

        if (
          (error?.status === 401 && error?.message === "Access not allowed") ||
          (error?.response?.status === 401 &&
            error?.message === "Access not allowed")
        ) {
          safeToast.error(error?.message);
          router.push("/login");
          return;
        }
      } finally {
        setIsLoading(false);
      }
    },
    [router]
  );

  return { isLoading, sendRequest };
};

export default useApiRequest;
