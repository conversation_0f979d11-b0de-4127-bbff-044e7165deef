import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/Components/Header/Header";
import ProgressLoader from "@/Components/Common/ProgressLoader";

const interFont = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${interFont.variable} ${interFont.variable} antialiased className="min-h-screen bg-gray-900 text-gray-100"`}
      >
        <ProgressLoader />
        <Header />
        <main>{children}</main>
      </body>
    </html>
  );
}
